# Niche Mono Sukhumvit - Bearing Condo Listing

A beautiful React-based website for a luxury condo listing in Bangkok, Thailand. This project has been converted from a static HTML website to a modern React application while maintaining 100% visual fidelity.

## 🏢 Project Overview

This is a professional real estate listing website for a 33 sqm corner unit on the 25th floor of Niche Mono Sukhumvit - Bearing condominium. The website features:

- **Hero Section** with stunning building imagery
- **Interactive Gallery** with category filtering and auto-play slider
- **Facilities Showcase** with detailed amenities
- **Floor Plans** and unit layouts
- **Location Information** with nearby amenities
- **Contact Information** for direct owner sales

## ✨ Features

### 🎨 Design & UX
- **100% Visual Fidelity** - Exact replica of original HTML design
- **Responsive Design** - Works perfectly on all devices
- **Smooth Animations** - Beautiful gradients and hover effects
- **Thai Language Support** - Full Thai language content

### 🖼️ Image Handling
- **Improved Image Loading** - Fixed intermittent loading issues
- **Loading States** - Graceful loading indicators
- **Error Handling** - Fallback displays for failed images
- **Lazy Loading** - Optimized performance

### 🎛️ Interactive Components
- **Gallery Slider** - Auto-playing with manual controls
- **Category Filtering** - Filter images by room type
- **Smooth Navigation** - Anchor-based section navigation
- **Hover Effects** - Enhanced user interactions

## 🚀 Quick Start

### Development Mode

```bash
# Navigate to React app directory
cd react-app

# Install dependencies
npm install

# Start development server
npm run dev
```

The development server will start at `http://localhost:5173`

### Production Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## 🐳 Docker Deployment

### Using Docker Compose (Recommended)

```bash
# Build and run with Docker Compose
docker-compose -f docker-compose-react.yml up --build

# Run in background
docker-compose -f docker-compose-react.yml up -d --build
```

The application will be available at `http://localhost:3000`

### Manual Docker Commands

```bash
# Build the Docker image
docker build -t niche-mono-react ./react-app

# Run the container
docker run -d -p 3000:80 --name niche-mono-app niche-mono-react

# Stop and remove container
docker stop niche-mono-app && docker rm niche-mono-app
```

## รูปภาพที่ต้องเพิ่ม

คุณต้องเพิ่มรูปภาพเหล่านี้ในโฟลเดอร์ `images/`: **รวม 10 รูป** (FACILITIES ใช้รูปจริงแล้ว)

### Required Images:
1. `hero-building.jpg` - รูปหน้าอาคาร Niche Mono
2. `floorplan.jpg` - แปลนชั้น 25
3. `unit-layout.jpg` - แปลนห้อง 33 ตร.ม.
4. `location-map.jpg` - แผนที่ตำแหน่งโครงการ

### Gallery Images:
5. `gallery-1.jpg` - รูปห้องนอน
6. `gallery-2.jpg` - รูปห้องน้ำ
7. `gallery-3.jpg` - รูปครัว
8. `gallery-4.jpg` - รูประเบียง
9. `gallery-5.jpg` - รูปวิวจากห้อง
10. `gallery-6.jpg` - รูปห้องโดยรวม

### 🆕 FACILITIES Images (รูปจริง 11 รูป): ✅ ใช้รูปจาก Sena แล้ว
- **LOBBY** (3 รูป): Elegance Lobby, Sky Lounge, Sky Meeting Room
- **POOL** (2 รูป): Swimming Pool, Jogging Track
- **FITNESS** (3 รูป): Gymnasium, Boxing Room, Yoga Room  
- **LIFESTYLE** (2 รูป): Co-working Space, Game Room
- **WELLNESS** (1 รูป): Sauna Room

**✅ ใช้รูปจริงจาก sena.co.th แล้ว - ไม่ต้องเพิ่มรูปเอง!**

## ข้อมูลที่สามารถแก้ไขได้

### ราคาและรายละเอียด
- ราคาปัจจุบัน: 4,500,000 บาท
- ชั้น: 25
- ขนาด: 33 ตร.ม.
- ประเภท: Studio ห้องมุม

### ข้อมูลติดต่อ
- เบอร์โทร: 081-234-5678
- Line ID: @nichecondo
- Email: <EMAIL>

## การติดตั้งและใช้งาน

1. วางไฟล์ทั้งหมดในโฟลเดอร์ web server
2. เพิ่มรูปภาพในโฟลเดอร์ `images/`
3. แก้ไขข้อมูลติดต่อในไฟล์ `index.html`
4. เปิดไฟล์ `index.html` ในเบราว์เซอร์

## การปรับแต่ง

### แก้ไขข้อมูลติดต่อ
แก้ไขในไฟล์ `index.html` ที่ส่วน contact section และ footer

### แก้ไขราคา
แก้ไขในไฟล์ `index.html` ที่ส่วน hero section

### แก้ไขสี theme
แก้ไขในไฟล์ `css/style.css` ที่ส่วน CSS variables หรือ gradient colors

### เพิ่ม Google Analytics
เพิ่ม tracking code ใน `js/script.js` ที่ฟังก์ชัน `trackClick`

## Browser Support
- Chrome (แนะนำ)
- Firefox
- Safari
- Edge

### 📱 ฟีเจอร์พิเศษ:

- **Contact Info Display** - แสดงข้อมูลติดต่อแบบ professional
- 🆕 **Dual Slider Galleries** - 2 gallery ระดับมืออาชีพ:
  - **Gallery Slider**: รูปห้องแบบใหญ่สวย (ALL, ห้องนอน, ห้องน้ำ, ครัว, ระเบียง, วิว)
  - **FACILITIES Slider**: สิ่งอำนวยความสะดวก (ALL, LOBBY, POOL, FITNESS, LIFESTYLE, WELLNESS)
- **Advanced Slider Features**:
  - Tab filtering ทั้ง 2 gallery
  - Auto-play ที่แตกต่างกัน (Gallery: 6s, FACILITIES: 5s)
  - Touch/Swipe support บนมือถือ
  - Hover overlay effects พร้อมรายละเอียด
  - Dots navigation และ arrow buttons
- **Smooth Scrolling** - คลิกเมนูจะเลื่อนไปส่วนที่ต้องการแบบ smooth
- **Beautiful Gradients** - ไล่เฉดสีฟ้าครามสวยงาม
- **Floating Animations** - เอฟเฟคลอยและ hover ที่น่าสนใจ
- **Mobile Optimized** - ใช้งานบนมือถือได้อย่างสมบูรณ์

## 🚀 Performance & Image Optimization

### 🎯 **ปัญหาที่แก้ไข:**
- รูปภาพใหญ่โหลดช้า → WebP/AVIF ลดขนาด 60%
- ทั้งหน้าโหลดพร้อมกัน → Lazy loading โหลดตามต้องการ
- ไม่มี caching → Progressive loading + performance monitoring

### 💡 **Solutions สำหรับ Proxmox LXC:**

**🥇 MinIO + Nginx Stack (แนะนำ):**
- S3-compatible storage + image optimization
- Web upload interface
- Automatic WebP/AVIF conversion
- Docker deployment บน LXC

**🥈 Chevereto (ง่ายที่สุด):**
- All-in-one image hosting platform
- Built-in optimization และ admin panel

**🥉 Static Optimization (ทำได้เลย):**
- ✅ เพิ่ม WebP support แล้ว
- ✅ เพิ่ม lazy loading แล้ว  
- ✅ เพิ่ม performance monitoring แล้ว

### 📈 **Expected Results:**
- **Loading Speed**: 4x เร็วขึ้น
- **Image Size**: 60-80% เล็กลง  
- **User Experience**: เรียบเก่า ไม่กระตุก
- **SEO Score**: เพิ่มขึ้นจาก page speed

### 🛠️ **Quick Start:**
```bash
# 1. Optimize รูปที่มี
./scripts/optimize-images.sh ./images ./optimized

# 2. Setup self-hosted (Proxmox LXC)
cd scripts && docker-compose up -d

# 3. ดู guide ละเอียด
cat IMAGE-OPTIMIZATION-GUIDE.md
```

---

**📖 อ่านเพิ่มเติม: `IMAGE-OPTIMIZATION-GUIDE.md`**

## 🔧 Bug Fixes & Troubleshooting

### ✅ **Gallery Slider Bug Fixed (Latest)**
**ปัญหา**: Gallery แสดงแค่ tabs ไม่มีรูปภาพ  
**สาเหตุ**: HTML structure ไม่ครบ, CSS conflicts, JavaScript initialization issues  
**การแก้ไข**: 
- ✅ แก้ HTML structure ให้ตรงกับ FACILITIES
- ✅ เพิ่ม CSS fixes แบบ !important  
- ✅ เพิ่ม JavaScript debugging และ error handling
- ✅ สร้าง debug tools สำหรับ troubleshooting

**📖 รายละเอียด**: `GALLERY-BUG-FIX.md`

### 🛠️ **Debug Tools Available**:
```javascript
// ใน Browser Console (F12)
debugGallery.checkElements()  // ตรวจสอบ HTML elements
debugGallery.checkStyles()    // ตรวจสอบ CSS styles  
debugGallery.manualInit()     // Manual initialization
debugGallery.goToSlide(2)     // ไปที่ slide ที่ต้องการ
```

### 🚀 **Quick Fix Steps**:
1. **Hard refresh** browser (Ctrl+F5)
2. **เปิด Console** (F12) ดู error logs
3. **ทดสอบ Gallery**: คลิก tabs, navigation, dots
4. **หากยังไม่ทำงาน**: รัน `debugGallery.manualInit()` ใน console

---

## Mobile Responsive
เว็บไซต์รองรับการแสดงผลบนมือถือและแท็บเล็ตทุกขนาด

---

**หมายเหตุ:** นี่เป็นเว็บไซต์สำหรับขายคอนโดส่วนตัว ไม่ใช่เว็บไซต์ของผู้พัฒนาโครงการ