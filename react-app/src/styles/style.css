/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Kanit', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 60px;
    color: #2c3e50;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #1e3c72 0%, #4682b4 50%, #87ceeb 100%);
}

.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9f4ff 50%, #f8fbff 100%);
}

.bg-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #4682b4 75%, #5f8fb8 100%);
    color: white;
}

.text-white {
    color: white !important;
}

/* Header */
.header {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.logo p {
    font-size: 0.9rem;
    color: #666;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav a:hover {
    background: linear-gradient(135deg, #1e3c72 0%, #4682b4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #1e3c72 0%, #4682b4 100%);
    transition: width 0.3s ease;
}

.nav a:hover::after {
    width: 100%;
}

/* Hero Section */


.hero-content {
    flex: 1;
    padding: 0 40px;
}

.hero h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.hero h3 {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-price {
    margin-bottom: 40px;
}

.price {
    font-size: 3.5rem;
    font-weight: 700;
    display: block;
}

.price-per-sqm {
    font-size: 1.2rem;
    opacity: 0.8;
}

.hero-highlights {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.highlight {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255,255,255,0.15);
    padding: 15px 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.highlight:hover {
    background: rgba(255,255,255,0.25);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(255,255,255,0.1);
}

.highlight i {
    font-size: 1.2rem;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

/* Information Section */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.info-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(30, 60, 114, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(70, 130, 180, 0.1);
}

.info-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(30, 60, 114, 0.15);
    border-color: rgba(70, 130, 180, 0.3);
}

.info-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 25px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-card ul {
    list-style: none;
}

.info-card li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.info-card li:last-child {
    border-bottom: none;
}

.info-card strong {
    color: #2c3e50;
    min-width: 120px;
    display: inline-block;
}

/* Floor Plan Section */
.floorplan-content {
    text-align: center;
}

.floorplan-image {
    position: relative;
    display: inline-block;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.floorplan-image img {
    max-width: 100%;
    height: auto;
    display: block;
}

.floorplan-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 30px;
    text-align: left;
}

.floorplan-info h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

/* Unit Type Section */
.unit-type-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.unit-layout img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.unit-details h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 30px;
    color: #2c3e50;
}

.features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 40px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.feature i {
    font-size: 1.5rem;
    color: #1e3c72; /* Fallback color */
    background: linear-gradient(135deg, #1e3c72 0%, #4682b4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.unit-highlights h4 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #2c3e50;
}

.unit-highlights ul {
    list-style: none;
}

.unit-highlights li {
    padding: 8px 0;
    position: relative;
    padding-left: 25px;
}

.unit-highlights li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

/* Gallery Section - Now using slider (old grid styles disabled) */
.gallery-grid {
    display: none; /* Disabled - now using slider */
}

.gallery-item {
    display: none; /* Disabled - now using slider */
}

.gallery-caption {
    display: none; /* Disabled - now using slider */
}

/* Facilities Section */
.facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.facility-card {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(30, 60, 114, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(70, 130, 180, 0.1);
}

.facility-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(30, 60, 114, 0.15);
    border-color: rgba(70, 130, 180, 0.3);
}

.facility-card i {
    font-size: 3rem;
    color: #1e3c72; /* Fallback color */
    background: linear-gradient(135deg, #1e3c72 0%, #4682b4 50%, #87ceeb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
}

.facility-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2c3e50;
}

.facility-card p {
    color: #666;
    line-height: 1.6;
}

/* Map Section */
.map-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: center;
}

.map-container img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.location-info h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 30px;
    color: #2c3e50;
}

.location-features {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.location-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(30, 60, 114, 0.1);
    border: 1px solid rgba(70, 130, 180, 0.1);
    transition: all 0.3s ease;
}

.location-item:hover {
    transform: translateX(5px);
    box-shadow: 0 8px 20px rgba(30, 60, 114, 0.15);
    border-color: rgba(70, 130, 180, 0.3);
}

.location-item i {
    font-size: 1.5rem;
    color: #1e3c72; /* Fallback color */
    background: linear-gradient(135deg, #1e3c72 0%, #4682b4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    min-width: 30px;
}

/* Nearby Places Section */
.nearby-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
}

.nearby-category {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(30, 60, 114, 0.1);
    border: 1px solid rgba(70, 130, 180, 0.1);
    transition: all 0.3s ease;
}

.nearby-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(30, 60, 114, 0.15);
    border-color: rgba(70, 130, 180, 0.3);
}

.nearby-category h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 25px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 15px;
}

.nearby-category i {
    color: #1e3c72; /* Fallback color */
    background: linear-gradient(135deg, #1e3c72 0%, #4682b4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nearby-category ul {
    list-style: none;
}

.nearby-category li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    color: #666;
}

.nearby-category li:last-child {
    border-bottom: none;
}

/* Contact Section */
.contact-content {
    display: flex;
    justify-content: center;
    align-items: center;
}

.contact-info-center {
    text-align: center;
    max-width: 600px;
}

.contact-info-center h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 30px;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-bottom: 40px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.contact-method i {
    font-size: 1.5rem;
    color: #1e3c72; /* Darker blue for better contrast on lighter gradient background */
    min-width: 30px;
}

.contact-method div {
    display: flex;
    flex-direction: column;
}

.contact-method strong {
    margin-bottom: 5px;
}

.contact-schedule h4 {
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.contact-schedule p {
    opacity: 0.9;
    margin-bottom: 5px;
}

.contact-form h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 30px;
}

.contact-form form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-form input,
.contact-form textarea {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 10px;
    font-family: 'Kanit', sans-serif;
    font-size: 1rem;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #4682b4;
    box-shadow: 0 0 0 3px rgba(70, 130, 180, 0.1);
}

.contact-form button {
    padding: 15px 30px;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #4682b4 50%, #5f8fb8 75%, #87ceeb 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-family: 'Kanit', sans-serif;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 5px 15px rgba(30, 60, 114, 0.3);
}

.contact-form button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(30, 60, 114, 0.4);
}

.contact-form button:hover {
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #0f1419 0%, #1a252f 25%, #2c3e50 50%, #1e3c72 100%);
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
}

.footer-info h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.footer-info p {
    opacity: 0.8;
    margin-bottom: 5px;
}

.footer-contact p {
    margin-bottom: 8px;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #34495e;
    opacity: 0.6;
}

/* Completely remove mock styling for real images */
#unit-layout-img,
#hero-img,
#floorplan-img,
#location-map,
#gallery-1,
#gallery-2,
#gallery-3,
#gallery-4,
#gallery-5,
#gallery-6 {
    background: none !important;
    position: static !important;
}

#unit-layout-img::after,
#hero-img::after,
#floorplan-img::after,
#location-map::after,
#gallery-1::after,
#gallery-2::after,
#gallery-3::after,
#gallery-4::after,
#gallery-5::after,
#gallery-6::after {
    display: none !important;
    content: none !important;
}

/* Ensure real images display properly */
img[src*=".jpg"], 
img[src*=".png"], 
img[src*=".gif"], 
img[src*=".jpeg"] {
    background: none !important;
    max-width: 100%;
    height: auto;
    display: block;
}

img[src*=".jpg"]::after, 
img[src*=".png"]::after, 
img[src*=".gif"]::after, 
img[src*=".jpeg"]::after {
    display: none !important;
}

/* Mock Image Placeholders - Only for images without file extensions */
img:not([src*=".jpg"]):not([src*=".png"]):not([src*=".gif"]):not([src*=".jpeg"]):not([id]) {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    position: relative;
}

img:not([src*=".jpg"]):not([src*=".png"]):not([src*=".gif"]):not([src*=".jpeg"]):not([id])::after {
    content: '📷 รูปภาพจะถูกเพิ่มเข้ามาในภายหลัง';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 0.9rem;
    white-space: nowrap;
}

/* Responsive Design */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero {
    margin-top: 100px;
    padding: 60px 0;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #4682b4 50%, #5f8fb8 75%, #87ceeb 100%);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    color: white;
    display: flex;
    align-items: center;
    min-height: 70vh;
}

.hero-image img {
    animation: float 6s ease-in-out infinite;
}

@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 20px;
    }
    
    .nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
    
    .hero {
        flex-direction: column;
        text-align: center;
        margin-top: 150px;
    }
    
    .hero h2 {
        font-size: 2rem;
    }
    
    .hero-highlights {
        justify-content: center;
    }
    
    .unit-type-grid,
    .map-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .features {
        grid-template-columns: 1fr;
    }
    
    .section {
        padding: 60px 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
}