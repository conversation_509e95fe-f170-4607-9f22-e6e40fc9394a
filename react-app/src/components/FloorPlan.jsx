import React, { useState } from 'react'

const FloorPlan = () => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const handleImageError = () => {
    setImageError(true)
    console.error('Floor plan image failed to load')
  }

  return (
    <section id="floorplan" className="section bg-light">
      <div className="container">
        <h2 className="section-title">แปลนชั้น</h2>
        <div className="floorplan-content">
          <div className="floorplan-image">
            {!imageLoaded && !imageError && (
              <div style={{ 
                width: '100%', 
                height: '400px', 
                background: '#f0f0f0', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                borderRadius: '15px'
              }}>
                Loading floor plan...
              </div>
            )}
            <img 
              src="https://minio-api.wattanachai.dev/nich-mono/floorplan.webp" 
              alt="แปลนชั้น 25" 
              id="floorplan-img"
              onLoad={handleImageLoad}
              onError={handleImageError}
              style={{ display: imageLoaded ? 'block' : 'none' }}
            />
            {imageError && (
              <div style={{ 
                width: '100%', 
                height: '400px', 
                background: '#f0f0f0', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                borderRadius: '15px',
                color: '#666'
              }}>
                Floor plan image unavailable
              </div>
            )}
            <div className="floorplan-info">
              <h3>ชั้นที่ 25</h3>
              <p>ห้องมุม - ตำแหน่งที่ดีที่สุด VVIP</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FloorPlan
