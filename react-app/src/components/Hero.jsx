import React, { useState } from 'react'

const Hero = () => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const handleImageError = () => {
    setImageError(true)
    console.error('Hero image failed to load')
  }

  return (
    <section className="hero">
      <div className="hero-content">
        <h2>ขาย คอนโด Niche Mono Sukhumvit - Bearing เจ้าของขายเอง</h2>
        <h3>ชั้น 25 มุม ตำแหน่ง VVIP • 33 ตร.ม. • วิวสวย</h3>
        <div className="hero-price">
          <span className="price">฿ 3,100,000</span>
          <span className="price-per-sqm">(฿93,939 ต่อ ตร.ม.)</span>
        </div>
        <div className="hero-highlights">
          <div className="highlight">
            <i className="fas fa-building"></i>
            <span>ชั้น 25</span>
          </div>
          <div className="highlight">
            <i className="fas fa-home"></i>
            <span>33 ตร.ม.</span>
          </div>
          <div className="highlight">
            <i className="fas fa-star"></i>
            <span>ห้องมุม</span>
          </div>
          <div className="highlight">
            <i className="fas fa-train"></i>
            <span>BTS แบริ่ง</span>
          </div>
        </div>
      </div>
      <div className="hero-image">
        {!imageLoaded && !imageError && (
          <div style={{ 
            width: '100%', 
            height: '400px', 
            background: '#f0f0f0', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            borderRadius: '15px'
          }}>
            Loading...
          </div>
        )}
        <img 
          src="https://minio-api.wattanachai.dev/nich-mono/hero-building.jpg" 
          alt="Niche Mono Sukhumvit - Bearing" 
          id="hero-img"
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{ display: imageLoaded ? 'block' : 'none' }}
        />
        {imageError && (
          <div style={{ 
            width: '100%', 
            height: '400px', 
            background: '#f0f0f0', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            borderRadius: '15px',
            color: '#666'
          }}>
            Image unavailable
          </div>
        )}
      </div>
    </section>
  )
}

export default Hero
