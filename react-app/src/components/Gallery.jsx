import React, { useState, useEffect } from 'react'

const Gallery = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [activeTab, setActiveTab] = useState('all')

  const galleryData = [
    {
      category: 'bedroom',
      src: 'https://sena.co.th/content/images/2022-12-04/1670363643-nicheskvbearingelegancelobby.jpg',
      alt: 'ห้องนอน',
      title: 'ห้องนอน',
      description: 'ห้องนอนสว่างสบาย รับแสงธรรมชาติดี'
    },
    {
      category: 'bedroom',
      src: 'https://sena.co.th/content/images/2022-12-04/1670312592-nicheskvbearingskylounge.jpg',
      alt: 'ห้องนอนมุมอื่น',
      title: 'ห้องนอนมุมอื่น',
      description: 'มุมมองห้องนอนจากอีกมุมหนึ่ง แสดงให้เห็นการใช้พื้นที่อย่างคุ้มค่า'
    },
    {
      category: 'bathroom',
      src: 'https://sena.co.th/content/images/2022-12-04/1670320979-nicheskvbearingskymeeting.jpg',
      alt: 'ห้องน้ำ',
      title: 'ห้องน้ำ',
      description: 'ห้องน้ำสะอาด ครบครันด้วยสุขภัณฑ์คุณภาพดี'
    },
    {
      category: 'bathroom',
      src: 'https://sena.co.th/content/images/2022-12-04/1670287134-nicheskvbearingpool.jpg',
      alt: 'ห้องน้ำรายละเอียด',
      title: 'ห้องน้ำรายละเอียด',
      description: 'รายละเอียดห้องน้ำ อ่างล้างมือและเครื่องสุขภัณฑ์'
    },
    {
      category: 'kitchen',
      src: 'https://sena.co.th/content/images/2022-12-04/1670412213-nicheskvbearingjoggingtrack.jpg',
      alt: 'ครัว',
      title: 'ครัว',
      description: 'ครัวบิลท์อินครบครัน พร้อมเครื่องใช้ไฟฟ้าและพื้นที่เก็บของ'
    },
    {
      category: 'kitchen',
      src: 'https://sena.co.th/content/images/2022-12-04/1670290035-nicheskvbearinggym.jpg',
      alt: 'ครัวรายละเอียด',
      title: 'ครัวรายละเอียด',
      description: 'รายละเอียดครัว เครื่องใช้ไฟฟ้าและบิลท์อิน'
    },
    {
      category: 'balcony',
      src: 'https://sena.co.th/content/images/2022-12-04/1670295741-nicheskvbearingboxingroom.jpg',
      alt: 'ระเบียง',
      title: 'ระเบียง',
      description: 'ระเบียงกว้างขวาง เหมาะสำหรับนั่งชมวิวและพักผ่อน'
    },
    {
      category: 'balcony',
      src: 'https://sena.co.th/content/images/2022-12-04/1670292076-nicheskvbearingyogaroom.jpg',
      alt: 'ระเบียงพร้อมวิว',
      title: 'ระเบียงพร้อมวิว',
      description: 'ระเบียงพร้อมวิวสวยงาม เหมาะสำหรับผ่อนคลาย'
    },
    {
      category: 'view',
      src: 'https://sena.co.th/content/images/2022-12-04/1670301541-nicheskvbearingcoworkingspace.jpg',
      alt: 'วิวจากห้อง',
      title: 'วิวจากห้อง',
      description: 'วิวเมืองและแม่น้ำเจ้าพระยาสวยงาม จากชั้น 25'
    },
    {
      category: 'view',
      src: 'https://sena.co.th/content/images/2022-12-04/1670295664-nicheskvbearinggameroom.jpg',
      alt: 'วิวกลางคืน',
      title: 'วิวกลางคืน',
      description: 'วิวยามค่ำคืนสวยงาม แสงไฟเมืองส่องประกาย'
    },
    {
      category: 'bedroom',
      src: 'https://sena.co.th/content/images/2022-12-04/1670360398-nicheskvbearingsaunaroom.jpg',
      alt: 'ห้องโดยรวม',
      title: 'ห้องโดยรวม',
      description: 'มุมมองภาพรวมของห้อง การใช้พื้นที่อย่างคุ้มค่า'
    }
  ]

  const filteredSlides = activeTab === 'all' 
    ? galleryData 
    : galleryData.filter(slide => slide.category === activeTab)

  const tabs = [
    { id: 'all', label: 'ALL' },
    { id: 'bedroom', label: 'ห้องนอน' },
    { id: 'bathroom', label: 'ห้องน้ำ' },
    { id: 'kitchen', label: 'ครัว' },
    { id: 'balcony', label: 'ระเบียง' },
    { id: 'view', label: 'วิว' }
  ]

  const handleTabChange = (tabId) => {
    setActiveTab(tabId)
    setCurrentSlide(0)
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % filteredSlides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => prev === 0 ? filteredSlides.length - 1 : prev - 1)
  }

  const goToSlide = (index) => {
    setCurrentSlide(index)
  }

  // Auto-play functionality
  useEffect(() => {
    const interval = setInterval(nextSlide, 6000)
    return () => clearInterval(interval)
  }, [filteredSlides.length])

  return (
    <section id="gallery" className="section bg-light">
      <div className="container">
        <h2 className="section-title">แกลเลอรี่</h2>
        <div className="gallery-tabs">
          {tabs.map(tab => (
            <button 
              key={tab.id}
              className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => handleTabChange(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </div>
        
        <div className="gallery-slider">
          <div className="slider-container">
            <div 
              className="slider-track" 
              id="gallery-track"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {filteredSlides.map((slide, index) => (
                <div key={index} className="gallery-slide" data-category={slide.category}>
                  <div className="gallery-image">
                    <img src={slide.src} alt={slide.alt} />
                    <div className="gallery-overlay">
                      <h4>{slide.title}</h4>
                      <p>{slide.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <button className="slider-btn prev-btn" onClick={prevSlide}>
            <i className="fas fa-chevron-left"></i>
          </button>
          <button className="slider-btn next-btn" onClick={nextSlide}>
            <i className="fas fa-chevron-right"></i>
          </button>
          
          <div className="slider-dots">
            {filteredSlides.map((_, index) => (
              <div 
                key={index}
                className={`dot ${currentSlide === index ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
              ></div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default Gallery
