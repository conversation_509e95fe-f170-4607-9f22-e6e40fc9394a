import React from 'react'

const Nearby = () => {
  const nearbyPlaces = [
    {
      icon: 'fas fa-shopping-cart',
      title: 'ช้อปปิ้ง',
      places: [
        'Imperial World Samrong',
        'Bitec Bangna',
        'The Bangkok Mall (ในอนาคต)',
        'Mega Bangna',
        'Central Bangna'
      ]
    },
    {
      icon: 'fas fa-hospital',
      title: 'โรงพยาบาล',
      places: [
        'โรงพยาบาล IMH Bearing  ฝั่งตรงข้าม (ในอนาคต)',
        'โรงพยาบาลไทยนครินทร์',
        'โรงพยาบาลสำโรง',
        'โรงพยาบาลศิครินทร์'
      ]
    },
    {
      icon: 'fas fa-graduation-cap',
      title: 'การศึกษา',
      places: [
        'โรงเรียนนานาชาติเซนต์แอนดรูว์ส',
        'โรงเรียนนานาชาติเบิร์คลีย์',
        'โรงเรียนนานาชาติบางกอกพัฒนา',
        'โรงเรียนเซนต์โยเซฟ บางนา'
      ]
    }
  ]

  return (
    <section id="nearby" className="section">
      <div className="container">
        <h2 className="section-title">สถานที่ใกล้เคียง</h2>
        <div className="nearby-grid">
          {nearbyPlaces.map((category, index) => (
            <div key={index} className="nearby-category">
              <h3>
                <i className={category.icon}></i> {category.title}
              </h3>
              <ul>
                {category.places.map((place, placeIndex) => (
                  <li key={placeIndex}>{place}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Nearby
