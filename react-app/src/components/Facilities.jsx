import React from 'react'

const Facilities = () => {
  const facilities = [
    {
      icon: 'fas fa-swimming-pool',
      title: 'สระว่ายน้ำ',
      description: 'สระว่ายน้ำกลางแจ้ง วิวสวยงาม'
    },
    {
      icon: 'fas fa-dumbbell',
      title: 'ฟิตเนส',
      description: 'ห้องออกกำลังกายครบครัน'
    },
    {
      icon: 'fas fa-shield-alt',
      title: 'รักษาความปลอดภัย',
      description: 'รักษาความปลอดภัย 24 ชม. CCTV'
    },
    {
      icon: 'fas fa-concierge-bell',
      title: 'ล็อบบี้',
      description: 'ล็อบบี้หรูหรา พร้อมต้อนรับ'
    },
    {
      icon: 'fas fa-key',
      title: 'Key Card',
      description: 'ระบบ Key Card'
    },
    {
      icon: 'fas fa-face-smile',
      title: 'Face Scan',
      description: 'ระบบแสกนหน้าเข้าลิฟต์ ป้องกัน Airbnb ได้ดี'
    },
    {
      icon: 'fas fa-wifi',
      title: 'WiFi',
      description: 'WiFi ฟรีบริเวณส่วนกลาง'
    },
    {
      icon: 'fas fa-leaf',
      title: 'สวนสวย',
      description: 'สวนภูมิทัศน์และพื้นที่พักผ่อน'
    }
  ]

  return (
    <section id="facilities" className="section">
      <div className="container">
        <h2 className="section-title">สิ่งอำนวยความสะดวก</h2>
        <div className="facilities-grid">
          {facilities.map((facility, index) => (
            <div key={index} className="facility-card">
              <i className={facility.icon}></i>
              <h3>{facility.title}</h3>
              <p>{facility.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Facilities
